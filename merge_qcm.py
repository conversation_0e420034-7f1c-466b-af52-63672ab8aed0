def merge_files(questions_file, explanations_file, output_file):
    # Read the questions file
    with open(questions_file, 'r', encoding='utf-8') as f:
        questions_content = f.read()

    # Read the explanations file
    with open(explanations_file, 'r', encoding='utf-8') as f:
        explanations_content = f.read()

    # Split questions into individual questions
    questions = questions_content.split('####################')
    # Split explanations into individual explanations
    explanations = explanations_content.split('####################')

    # Clean up explanations (remove question numbers and empty lines)
    cleaned_explanations = []
    for exp in explanations:
        if exp.strip():
            exp_lines = exp.strip().split('\n')
            # Skip the first line if it contains the question
            if len(exp_lines) > 0 and any(exp_lines[0].endswith(x) for x in ['.', ':', '=']):
                exp_lines = exp_lines[1:]
            cleaned_exp = '\n'.join(line for line in exp_lines if line.strip())
            cleaned_explanations.append(cleaned_exp.strip())

    # Merge questions with explanations
    merged_content = []
    for q, e in zip(questions, cleaned_explanations):
        if q.strip():  # Skip empty questions
            merged_content.append(f"{q.strip()}\nexplication: \n{e}\n####################\n")

    # Write the merged content to output file
    with open(output_file, 'w', encoding='utf-8') as f:
        f.write('\n'.join(merged_content))

if __name__ == "__main__":
    # Example usage
    questions_file = "questions.txt"  # Replace with your questions file name
    explanations_file = "explanations.txt"  # Replace with your explanations file name
    output_file = "merged_output.txt"  # Output file name
    
    try:
        merge_files(questions_file, explanations_file, output_file)
        print("Files merged successfully! Check", output_file)
    except Exception as e:
        print("An error occurred:", str(e)) 