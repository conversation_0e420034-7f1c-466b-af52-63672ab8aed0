import subprocess
import os

def convert_odt_to_docx(odt_filepath, docx_filepath=None):
    """
    Converts an ODT file to DOCX using LibreOffice.

    Args:
        odt_filepath: Path to the ODT file.
        docx_filepath: Path to save the DOCX file. If None, it will save in the same directory
                       as the ODT file with the same name but a .docx extension.

    Returns:
        True if the conversion was successful, False otherwise.
    """
    if not os.path.exists(odt_filepath):
        print(f"Error: ODT file not found at {odt_filepath}")
        return False

    if docx_filepath is None:
        docx_filepath = os.path.splitext(odt_filepath)[0] + ".docx"

    try:
        # Construct the LibreOffice command
        command = [
            "libreoffice",
            "--headless",
            "--convert-to",
            "docx",
            odt_filepath,
            "--outdir",
            os.path.dirname(docx_filepath),  # Specify the output directory
        ]

        # Execute the command
        process = subprocess.run(command, capture_output=True, text=True, check=True)

        # Print any error messages from LibreOffice (for debugging)
        if process.stderr:
            print(f"LibreOffice conversion error: {process.stderr}")

        print(f"Successfully converted {odt_filepath} to {docx_filepath}")
        return True

    except subprocess.CalledProcessError as e:
        print(f"Error during conversion: {e}")
        print(f"Command: {e.cmd}")
        print(f"Return code: {e.returncode}")
        print(f"Stdout: {e.stdout}")
        print(f"Stderr: {e.stderr}")  # Crucial for debugging
        return False
    except FileNotFoundError:
        print("Error: LibreOffice not found.  Make sure it's installed and in your system's PATH.")
        return False
    except Exception as e:
        print(f"An unexpected error occurred: {e}")
        return False


# Example usage
odt_file = "C:/Users/<USER>/OneDrive/Bureau/2-year/qcm/✅❌Génétique/Classification/Caryotype normale.génétique.odt"  # Replace with the actual path
docx_file = "C:/Users/<USER>/OneDrive/Bureau/2-year/qcm/✅❌Génétique/Classification/docx" # Replace with the desired path or None for same directory

if convert_odt_to_docx(odt_file, docx_file):
    print("Conversion successful!")
else:
    print("Conversion failed.")