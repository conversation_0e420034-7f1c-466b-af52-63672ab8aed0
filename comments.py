import os
import tkinter as tk
from tkinter import filedialog
from docx import Document
from docx.shared import Pt
from docx.enum.text import WD_LINE_SPACING
import logging

# --- Configuration ---
TARGET_FONT_NAME = 'Calibri'
TARGET_FONT_SIZE = Pt(14)  # Font size in points
TARGET_LINE_SPACING = 1.5
# Set up logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
# --- End Configuration ---

def set_paragraph_formatting(paragraph):
    """Applies desired line spacing to a paragraph."""
    paragraph.paragraph_format.line_spacing = TARGET_LINE_SPACING
    # It's often good practice to set the rule as well
    if TARGET_LINE_SPACING == 1.0:
         paragraph.paragraph_format.line_spacing_rule = WD_LINE_SPACING.SINGLE
    elif TARGET_LINE_SPACING == 1.5:
         paragraph.paragraph_format.line_spacing_rule = WD_LINE_SPACING.ONE_POINT_FIVE
    elif TARGET_LINE_SPACING == 2.0:
         paragraph.paragraph_format.line_spacing_rule = WD_LINE_SPACING.DOUBLE
    # Add other rules if needed, e.g., MULTIPLE, EXACTLY, AT_LEAST

def set_run_formatting(run):
    """Applies desired font name and size to a run, preserving bold/color."""
    run.font.name = TARGET_FONT_NAME
    run.font.size = TARGET_FONT_SIZE
    # Existing bold, italic, color etc. on the run object are preserved
    # unless explicitly overwritten here. We are only setting name and size.

def process_docx_file(file_path):
    """Opens, modifies, and saves a DOCX file."""
    try:
        logging.info(f"Processing file: {file_path}")
        document = Document(file_path)

        # --- Process Paragraphs and Runs ---
        for paragraph in document.paragraphs:
            set_paragraph_formatting(paragraph)
            for run in paragraph.runs:
                set_run_formatting(run)

        # --- Process Tables (Optional but recommended) ---
        # Formatting within tables might also need adjustment
        for table in document.tables:
            for row in table.rows:
                for cell in row.cells:
                    # Apply formatting to paragraphs within each cell
                    for paragraph in cell.paragraphs:
                        set_paragraph_formatting(paragraph)
                        for run in paragraph.runs:
                            set_run_formatting(run)

        # --- Save the modified document ---
        # This overwrites the original file!
        document.save(file_path)
        logging.info(f"Successfully updated: {file_path}")
        return True

    except Exception as e:
        logging.error(f"Could not process file {file_path}: {e}")
        return False

def main():
    """Main function to select folder and process files."""
    # --- Set up Tkinter for folder selection ---
    root = tk.Tk()
    root.withdraw() # Hide the main Tkinter window

    # --- Ask user to select a folder ---
    logging.info("Please select the folder containing the DOCX files.")
    folder_path = filedialog.askdirectory(title="Select Folder with DOCX Files")

    if not folder_path:
        logging.warning("No folder selected. Exiting.")
        return

    logging.info(f"Selected folder: {folder_path}")
    logging.warning("--- IMPORTANT: This script will OVERWRITE original DOCX files! ---")
    logging.warning("--- Make sure you have backups if needed. ---")
    input("Press Enter to continue or Ctrl+C to cancel...") # Safety pause

    processed_count = 0
    failed_count = 0

    # --- Walk through the directory ---
    for root_dir, _, files in os.walk(folder_path):
        for filename in files:
            # Check if the file is a docx file (case-insensitive)
            if filename.lower().endswith('.docx'):
                file_path = os.path.join(root_dir, filename)
                if process_docx_file(file_path):
                    processed_count += 1
                else:
                    failed_count += 1

    # --- Completion Summary ---
    logging.info("---------------------------------------------")
    logging.info("Processing complete.")
    logging.info(f"Successfully processed files: {processed_count}")
    logging.info(f"Failed to process files: {failed_count}")
    logging.info("---------------------------------------------")


if __name__ == "__main__":
    main()