import os
import requests
import glob
import tkinter as tk
from tkinter import filedialog, messagebox, ttk
from dotenv import load_dotenv

# Load environment variables from .env file
load_dotenv()

# Get API key from environment variables
OPENROUTER_API_KEY = os.getenv("OPENROUTER_API_KEY")

# Constants for the app
UNITIES = [
    "UEI1_ Cardio Respiratoire",
    "UEI2_ Appareil Digestif",
    "UEI3_ Appareil Urinaire",
    "UEI4_ Système Endocrinien & Reproducteur",
    "UEI5_ Appareil Neurologique"
]

MODULES = [
    "Anatomie",
    "Histologie",
    "Biochimie",
    "Physiologie",
    "Biophysique",
    "Génétique",
    "Immunologie",
    "Microbiologie"
]

def extract_subject_from_filename(filename, api_key):
    """Use Gemini 2.0 Flash Lite through OpenRouter API to extract subject from filename"""
    
    url = "https://openrouter.ai/api/v1/chat/completions"
    
    headers = {
        "Authorization": f"Bearer {api_key}",
        "Content-Type": "application/json"
    }
    
    data = {
        "model": "google/gemini-2.0-flash-lite-001",
        "messages": [
            {
                "role": "user",
                "content": f"""Extract only the main medical subject from this filename (in french). 
                Return ONLY the subject name with no additional text or formatting.
                Example: For 'L'Aorte thoracique et artère pulmonaire.Anatomie.app cardio respiratoire.docx', return only 'L'Aorte thoracique et artère pulmonaire'.
                For 'Généralité_Sur_Les_Organes_Hématopoïétiques_Histologie_app_cardio', return only 'Généralité Sur Les Organes Hématopoïétiques'.
                Filename: {filename}"""
            }
        ],
        "temperature": 0.1,
        "max_tokens": 50
    }
    
    try:
        response = requests.post(url, headers=headers, json=data)
        response.raise_for_status()  # Raise exception for HTTP errors
        
        result = response.json()
        subject = result["choices"][0]["message"]["content"].strip()
        return subject
    except Exception as e:
        print(f"Error calling OpenRouter API: {e}")
        return None

def sanitize_filename(filename):
    """Replace characters that are invalid in Windows filenames"""
    invalid_chars = ['<', '>', ':', '"', '/', '\\', '|', '?', '*']
    for char in invalid_chars:
        filename = filename.replace(char, '-')
    return filename

class FileRenamerApp:
    def __init__(self, root):
        self.root = root
        self.root.title("Medical Files Renamer")
        self.root.geometry("800x600")
        
        self.files = []
        self.current_file_index = 0
        self.new_filenames = {}
        
        # Create main frame
        main_frame = ttk.Frame(root, padding="10")
        main_frame.pack(fill=tk.BOTH, expand=True)
        
        # Create buttons frame
        button_frame = ttk.Frame(main_frame)
        button_frame.pack(fill=tk.X, pady=10)
        
        # Select Files button
        select_btn = ttk.Button(button_frame, text="Select Files", command=self.select_files)
        select_btn.pack(side=tk.LEFT, padx=5)
        
        # Process All button
        process_btn = ttk.Button(button_frame, text="Process All Files", command=self.process_all_files)
        process_btn.pack(side=tk.LEFT, padx=5)
        
        # Rename All button
        rename_btn = ttk.Button(button_frame, text="Rename All Files", command=self.rename_all_files)
        rename_btn.pack(side=tk.LEFT, padx=5)
        
        # Create file info frame
        file_frame = ttk.LabelFrame(main_frame, text="File Information", padding="10")
        file_frame.pack(fill=tk.BOTH, expand=True, pady=10)
        
        # Original filename
        ttk.Label(file_frame, text="Original Filename:").grid(column=0, row=0, sticky=tk.W, pady=5)
        self.original_filename_var = tk.StringVar()
        ttk.Label(file_frame, textvariable=self.original_filename_var).grid(column=1, row=0, sticky=tk.W, pady=5)
        
        # Subject
        ttk.Label(file_frame, text="Subject:").grid(column=0, row=1, sticky=tk.W, pady=5)
        self.subject_var = tk.StringVar()
        self.subject_entry = ttk.Entry(file_frame, textvariable=self.subject_var, width=50)
        self.subject_entry.grid(column=1, row=1, sticky=tk.W, pady=5)
        
        # Detect subject button
        detect_btn = ttk.Button(file_frame, text="Detect Subject", command=self.detect_subject)
        detect_btn.grid(column=2, row=1, sticky=tk.W, padx=5)
        
        # Unity
        ttk.Label(file_frame, text="Unity:").grid(column=0, row=2, sticky=tk.W, pady=5)
        self.unity_var = tk.StringVar()
        unity_combo = ttk.Combobox(file_frame, textvariable=self.unity_var, width=50)
        unity_combo['values'] = [""] + UNITIES
        unity_combo.grid(column=1, row=2, sticky=tk.W, pady=5)
        
        # Apply Unity to All button
        apply_unity_btn = ttk.Button(file_frame, text="Apply to All", command=self.apply_unity_to_all)
        apply_unity_btn.grid(column=2, row=2, sticky=tk.W, padx=5)
        
        # Module
        ttk.Label(file_frame, text="Module:").grid(column=0, row=3, sticky=tk.W, pady=5)
        self.module_var = tk.StringVar()
        module_combo = ttk.Combobox(file_frame, textvariable=self.module_var, width=50)
        module_combo['values'] = MODULES
        module_combo.grid(column=1, row=3, sticky=tk.W, pady=5)
        
        # Apply Module to All button
        apply_module_btn = ttk.Button(file_frame, text="Apply to All", command=self.apply_module_to_all)
        apply_module_btn.grid(column=2, row=3, sticky=tk.W, padx=5)
        
        # New filename
        ttk.Label(file_frame, text="New Filename:").grid(column=0, row=4, sticky=tk.W, pady=5)
        self.new_filename_var = tk.StringVar()
        ttk.Label(file_frame, textvariable=self.new_filename_var).grid(column=1, row=4, sticky=tk.W, pady=5)
        
        # Update and navigation buttons
        nav_frame = ttk.Frame(file_frame)
        nav_frame.grid(column=1, row=5, sticky=tk.W, pady=10)
        
        self.prev_btn = ttk.Button(nav_frame, text="Previous", command=self.previous_file, state=tk.DISABLED)
        self.prev_btn.pack(side=tk.LEFT, padx=5)
        
        ttk.Button(nav_frame, text="Update", command=self.update_current_file).pack(side=tk.LEFT, padx=5)
        
        self.next_btn = ttk.Button(nav_frame, text="Next", command=self.next_file, state=tk.DISABLED)
        self.next_btn.pack(side=tk.LEFT, padx=5)
        
        # Files list
        list_frame = ttk.LabelFrame(main_frame, text="Files to Process", padding="10")
        list_frame.pack(fill=tk.BOTH, expand=True, pady=10)
        
        # Create a treeview to display the files
        self.files_tree = ttk.Treeview(list_frame, columns=("Original", "New"), show="headings")
        self.files_tree.heading("Original", text="Original Filename")
        self.files_tree.heading("New", text="New Filename")
        self.files_tree.column("Original", width=300)
        self.files_tree.column("New", width=400)
        self.files_tree.pack(fill=tk.BOTH, expand=True)
        
        # Create a status bar
        self.status_var = tk.StringVar()
        status_bar = ttk.Label(main_frame, textvariable=self.status_var, relief=tk.SUNKEN, anchor=tk.W)
        status_bar.pack(fill=tk.X, side=tk.BOTTOM, pady=5)
        
        self.status_var.set("Ready")
    
    def apply_unity_to_all(self):
        """Apply the currently selected Unity to all files"""
        if not self.files:
            messagebox.showinfo("Information", "Please select files first")
            return
        
        selected_unity = self.unity_var.get().strip()
        if not selected_unity:
            messagebox.showinfo("Information", "Please select a Unity first")
            return
        
        # Apply to all files
        for file in self.files:
            if file in self.new_filenames:
                # Update the unity in the stored parts
                self.new_filenames[file]['parts']['unity'] = selected_unity
                
                # Rebuild the new filename
                subject = self.new_filenames[file]['parts']['subject']
                module = self.new_filenames[file]['parts']['module']
                
                parts = []
                parts.append(subject)
                if module:
                    parts.append(module)
                if selected_unity:
                    parts.append(selected_unity)
                
                # Join parts and remove whitespace around periods
                new_name = ".".join(part.strip() for part in parts) + ".docx"
                new_name = sanitize_filename(new_name)
                
                self.new_filenames[file]['new_name'] = new_name
                
                # Update the treeview
                for item in self.files_tree.get_children():
                    values = self.files_tree.item(item, 'values')
                    if values[0] == os.path.basename(file):
                        self.files_tree.item(item, values=(values[0], new_name))
                        break
        
        # Update the current file display
        self.update_ui_for_current_file()
        self.status_var.set(f"Applied Unity '{selected_unity}' to all files")
    
    def apply_module_to_all(self):
        """Apply the currently selected Module to all files"""
        if not self.files:
            messagebox.showinfo("Information", "Please select files first")
            return
        
        selected_module = self.module_var.get().strip()
        if not selected_module:
            messagebox.showinfo("Information", "Please select a Module first")
            return
        
        # Apply to all files
        for file in self.files:
            if file in self.new_filenames:
                # Update the module in the stored parts
                self.new_filenames[file]['parts']['module'] = selected_module
                
                # Rebuild the new filename
                subject = self.new_filenames[file]['parts']['subject']
                unity = self.new_filenames[file]['parts']['unity']
                
                parts = []
                parts.append(subject)
                if selected_module:
                    parts.append(selected_module)
                if unity:
                    parts.append(unity)
                
                # Join parts and remove whitespace around periods
                new_name = ".".join(part.strip() for part in parts) + ".docx"
                new_name = sanitize_filename(new_name)
                
                self.new_filenames[file]['new_name'] = new_name
                
                # Update the treeview
                for item in self.files_tree.get_children():
                    values = self.files_tree.item(item, 'values')
                    if values[0] == os.path.basename(file):
                        self.files_tree.item(item, values=(values[0], new_name))
                        break
        
        # Update the current file display
        self.update_ui_for_current_file()
        self.status_var.set(f"Applied Module '{selected_module}' to all files")
    
    def select_files(self):
        """Allow user to select multiple files"""
        filetypes = (
            ("Word files","*.docx"),
            ("All files","*.*")
        )
        
        selected_files = filedialog.askopenfilenames(
            title="Select files",
            filetypes=filetypes
        )
        
        if selected_files:
            self.files = list(selected_files)
            self.current_file_index = 0
            self.new_filenames = {}
            
            # Clear the treeview
            for item in self.files_tree.get_children():
                self.files_tree.delete(item)
            
            # Add files to the treeview
            for file in self.files:
                self.files_tree.insert("", tk.END, values=(os.path.basename(file), ""))
            
            # Update UI for the first file
            self.update_ui_for_current_file()
            self.status_var.set(f"Selected {len(self.files)} files")
            
            # Enable/disable navigation buttons
            self.update_navigation_buttons()
    
    def update_ui_for_current_file(self):
        """Update UI with current file information"""
        if not self.files or self.current_file_index >= len(self.files):
            return
        
        current_file = self.files[self.current_file_index]
        basename = os.path.basename(current_file)
        
        self.original_filename_var.set(basename)
        
        # Reset fields
        self.subject_var.set("")
        self.unity_var.set("")
        self.module_var.set("")
        self.new_filename_var.set("")
        
        # If we already have processed this file, show the values
        if current_file in self.new_filenames:
            parts = self.new_filenames[current_file]['parts']
            self.subject_var.set(parts.get('subject', ''))
            self.unity_var.set(parts.get('unity', ''))
            self.module_var.set(parts.get('module', ''))
            self.new_filename_var.set(self.new_filenames[current_file]['new_name'])
    
    def detect_subject(self):
        """Detect subject for the current file using AI"""
        if not self.files or self.current_file_index >= len(self.files):
            return
        
        current_file = self.files[self.current_file_index]
        basename = os.path.basename(current_file)
        
        self.status_var.set(f"Detecting subject for {basename}...")
        self.root.update()
        
        # Call the API
        subject = extract_subject_from_filename(basename, OPENROUTER_API_KEY)
        
        if subject:
            self.subject_var.set(subject)
            self.update_new_filename()
            self.status_var.set(f"Subject detected: {subject}")
        else:
            self.status_var.set("Failed to detect subject. Please enter manually.")
    
    def update_new_filename(self):
        """Update the new filename based on current settings"""
        subject = self.subject_var.get().strip()
        unity = self.unity_var.get().strip()
        module = self.module_var.get().strip()
        
        if not subject:
            self.new_filename_var.set("")
            return
        
        # Build the new filename with Module before Unity (inverted order)
        parts = []
        parts.append(subject)
        
        if module:
            parts.append(module)
        
        if unity:
            parts.append(unity)
        
        # Join parts with periods - remove whitespace around periods
        new_name = ".".join(part.strip() for part in parts) + ".docx"
        new_name = sanitize_filename(new_name)
        
        self.new_filename_var.set(new_name)
        
        # Store the parts separately for later reference
        if self.files:
            current_file = self.files[self.current_file_index]
            self.new_filenames[current_file] = {
                'new_name': new_name,
                'parts': {
                    'subject': subject,
                    'unity': unity,
                    'module': module
                }
            }
            
            # Update the treeview
            for item in self.files_tree.get_children():
                values = self.files_tree.item(item, 'values')
                if values[0] == os.path.basename(current_file):
                    self.files_tree.item(item, values=(values[0], new_name))
                    break
    
    def update_current_file(self):
        """Update information for the current file"""
        if not self.files or self.current_file_index >= len(self.files):
            return
        
        # Call update_new_filename to generate and store the new filename
        self.update_new_filename()
        self.status_var.set("File information updated")
    
    def previous_file(self):
        """Navigate to the previous file"""
        if self.current_file_index > 0:
            self.current_file_index -= 1
            self.update_ui_for_current_file()
            self.update_navigation_buttons()
            self.status_var.set(f"Viewing file {self.current_file_index + 1} of {len(self.files)}")
    
    def next_file(self):
        """Navigate to the next file"""
        if self.current_file_index < len(self.files) - 1:
            self.current_file_index += 1
            self.update_ui_for_current_file()
            self.update_navigation_buttons()
            self.status_var.set(f"Viewing file {self.current_file_index + 1} of {len(self.files)}")
    
    def update_navigation_buttons(self):
        """Enable/disable navigation buttons based on current state"""
        if not self.files:
            self.prev_btn.config(state=tk.DISABLED)
            self.next_btn.config(state=tk.DISABLED)
            return
        
        self.prev_btn.config(state=tk.NORMAL if self.current_file_index > 0 else tk.DISABLED)
        self.next_btn.config(state=tk.NORMAL if self.current_file_index < len(self.files) - 1 else tk.DISABLED)
    
    def process_all_files(self):
        """Process all files to detect subjects automatically"""
        if not self.files:
            messagebox.showinfo("Information", "Please select files first")
            return
        
        # Save current index to restore later
        current_index = self.current_file_index
        
        for i, file in enumerate(self.files):
            basename = os.path.basename(file)
            self.status_var.set(f"Processing file {i+1} of {len(self.files)}: {basename}")
            self.root.update()
            
            # Set current file index
            self.current_file_index = i
            self.update_ui_for_current_file()
            
            # Detect subject
            subject = extract_subject_from_filename(basename, OPENROUTER_API_KEY)
            
            if subject:
                self.subject_var.set(subject)
                # Don't set unity or module automatically
                self.unity_var.set("")
                self.module_var.set("")
                # Update the new filename
                self.update_new_filename()
        
        # Restore the original index
        self.current_file_index = current_index
        self.update_ui_for_current_file()
        self.status_var.set(f"Processed {len(self.files)} files")
    
    def rename_all_files(self):
        """Rename all files based on the new filenames"""
        if not self.files:
            messagebox.showinfo("Information", "Please select files first")
            return
        
        if not self.new_filenames:
            messagebox.showinfo("Information", "Please process files first")
            return
        
        success_count = 0
        error_count = 0
        
        for file in self.files:
            if file in self.new_filenames:
                new_name = self.new_filenames[file]['new_name']
                
                # Ensure the new name is sanitized
                new_name = sanitize_filename(new_name)
                
                directory = os.path.dirname(file)
                
                # Normalize the path by using os.path.normpath
                new_path = os.path.normpath(os.path.join(directory, new_name))
                
                # Avoid paths longer than 260 characters (Windows limitation)
                if len(new_path) >= 260:
                    print(f"Path too long for {file}: {len(new_path)} characters")
                    error_count += 1
                    continue
                    
                try:
                    # Make sure the source file exists
                    if not os.path.exists(file):
                        print(f"Source file does not exist: {file}")
                        error_count += 1
                        continue
                        
                    # Check if destination already exists
                    if os.path.exists(new_path) and file.lower() != new_path.lower():
                        print(f"Destination file already exists: {new_path}")
                        error_count += 1
                        continue
                    
                    # Use a two-step process for case-insensitive systems
                    if file.lower() == new_path.lower() and file != new_path:
                        # For case changes on Windows, use a temporary filename
                        temp_path = new_path + ".temporary"
                        os.rename(file, temp_path)
                        os.rename(temp_path, new_path)
                    else:
                        os.rename(file, new_path)
                        
                    success_count += 1
                except Exception as e:
                    print(f"Error renaming {file} to {new_path}: {e}")
                    error_count += 1
        
        # Clear the files list and update UI
        self.files = []
        self.new_filenames = {}
        self.current_file_index = 0
        
        # Clear the treeview
        for item in self.files_tree.get_children():
            self.files_tree.delete(item)
        
        # Reset UI fields
        self.original_filename_var.set("")
        self.subject_var.set("")
        self.unity_var.set("")
        self.module_var.set("")
        self.new_filename_var.set("")
        
        # Update navigation buttons
        self.update_navigation_buttons()
        
        # Show result
        if error_count > 0:
            messagebox.showwarning("Warning", f"Renamed {success_count} files. Failed to rename {error_count} files.")
        else:
            messagebox.showinfo("Success", f"Successfully renamed {success_count} files.")
        
        self.status_var.set(f"Renamed {success_count} files. Failed: {error_count}")

if __name__ == "__main__":
    root = tk.Tk()
    app = FileRenamerApp(root)
    root.mainloop()