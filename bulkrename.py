import os
import re
import logging

# --- Configure Logging ---
log_format = '%(asctime)s - %(levelname)s - %(message)s'
logging.basicConfig(level=logging.INFO, format=log_format)
# Optional file logging:
# file_handler = logging.FileHandler('rename_log.txt', mode='w') # 'w' to overwrite log each run
# file_handler.setFormatter(logging.Formatter(log_format))
# logging.getLogger().addHandler(file_handler)


def normalize_text(text):
    """Cleans up text string for better matching (lowercase, spacing)."""
    if not isinstance(text, str): # Handle potential non-string parts
        text = str(text)
    normalized = text.lower().strip()
    normalized = re.sub(r'[_ ]+', ' ', normalized) # Replace underscores or multiple spaces
    return normalized

def create_composite_key(subject, type_):
    """Creates a unique tuple key from normalized subject and type."""
    return (normalize_text(subject), normalize_text(type_))

def rename_files(target_dir, source_dir, dry_run=True):
    """
    Renames files in source_dir to match the naming convention in target_dir,
    using a composite key (subject + type) for matching and logging changes.

    Args:
        target_dir (str): Path to the directory with the desired filenames (LEFT side).
        source_dir (str): Path to the directory with files to be renamed (RIGHT side).
        dry_run (bool): If True, only log what would be renamed. If False, perform renaming.
    """
    logging.info(f"--- Starting File Renaming ---")
    logging.info(f"Target Directory (Desired Names): {target_dir}")
    logging.info(f"Source Directory (Files to Rename): {source_dir}")
    logging.info(f"Dry Run Mode: {'ON' if dry_run else 'OFF'}")
    logging.info("-" * 30)

    if not os.path.isdir(target_dir):
        logging.error(f"Target directory not found: {target_dir}")
        return
    if not os.path.isdir(source_dir):
        logging.error(f"Source directory not found: {source_dir}")
        return

    target_files = [f for f in os.listdir(target_dir) if f.lower().endswith('.docx')]
    source_files = [f for f in os.listdir(source_dir) if f.lower().endswith('.docx')]

    if not target_files:
        logging.warning("No .docx files found in the target directory.")
    if not source_files:
        logging.warning("No .docx files found in the source directory.")

    # --- Build a map from composite key (subject, type) to target filename ---
    target_name_map = {}
    logging.info("Building target name map using (Subject, Type) key...")
    map_build_warnings = 0
    for filename in target_files:
        base_name, ext = os.path.splitext(filename)
        parts = base_name.split('.')
        # Expected Target Format: Subject.Type.UEI4_AppareilEndocrinien
        if len(parts) >= 3:
            subject_part = parts[0]
            type_part = parts[1] # Assume type is the second part
            composite_key = create_composite_key(subject_part, type_part)

            if composite_key in target_name_map:
                # This *shouldn't* happen if target filenames are truly unique combinations
                logging.warning(f"Duplicate composite key '{composite_key}' for target files '{target_name_map[composite_key]}' and '{filename}'. Using the latter.")
                map_build_warnings += 1
            target_name_map[composite_key] = filename
            # logging.info(f"  Mapped Key: {composite_key} -> '{filename}'") # Uncomment for detailed map creation
        else:
            logging.warning(f"Could not parse target filename format for key: {filename}")
            map_build_warnings += 1
    logging.info(f"Target map built with {len(target_name_map)} entries. ({map_build_warnings} warnings)")
    logging.info("-" * 30)

    # --- Iterate through source files and rename ---
    rename_count = 0
    match_fail_count = 0
    already_correct_count = 0
    rename_errors = 0
    logging.info("Processing source files for renaming...")
    for filename in source_files:
        old_path = os.path.join(source_dir, filename)
        base_name, ext = os.path.splitext(filename)

        # Expected Source Format: Appareil endocrinien.Subject.TYPE
        parts = base_name.split('.')
        subject_part = None
        type_part = None
        composite_key = None

        if base_name.lower().startswith("appareil endocrinien.") and len(parts) >= 3:
            subject_part = parts[1] # Subject is second part
            type_part = parts[-1]   # Type is last part before extension
            composite_key = create_composite_key(subject_part, type_part)
        else:
            # Attempt a fallback if needed, but the primary pattern is expected
            logging.warning(f"Source filename '{filename}' doesn't match expected 'Appareil endocrinien.Subject.TYPE' pattern.")

        if composite_key:
            # logging.info(f"  Source file: '{filename}', Composite Key: {composite_key}") # Debugging line

            if composite_key in target_name_map:
                new_filename = target_name_map[composite_key]
                new_path = os.path.join(source_dir, new_filename)

                if old_path == new_path:
                    already_correct_count += 1
                    continue # Don't log already correct files unless needed

                log_message = f"{filename} ----> {new_filename}"

                if not dry_run:
                    try:
                        os.rename(old_path, new_path)
                        logging.info(f"{log_message} (Renamed)")
                        rename_count += 1
                    except OSError as e:
                        logging.error(f"Error renaming '{filename}' to '{new_filename}': {e}")
                        rename_errors += 1
                else:
                    logging.info(f"{log_message} (Dry Run - Would Rename)")
                    rename_count += 1

            else:
                logging.warning(f"No match found for key {composite_key} from source file: {filename}")
                match_fail_count += 1
        else:
            # This case is now covered by the warning inside the loop
            match_fail_count += 1 # Count files that couldn't be parsed

    # --- Summary Logging ---
    logging.info("-" * 30)
    if dry_run:
        logging.info(f"--- Dry Run Complete ---")
        logging.info(f"Would rename {rename_count} files.")
    else:
        logging.info(f"--- Renaming Complete ---")
        logging.info(f"Successfully renamed {rename_count} files.")

    if already_correct_count > 0:
        logging.info(f"{already_correct_count} files already had the correct name.")
    if match_fail_count > 0:
         logging.warning(f"{match_fail_count} files could not be matched or parsed correctly.")
    if rename_errors > 0:
         logging.error(f"{rename_errors} errors occurred during renaming.")
    logging.info("-" * 30)


# --- HOW TO USE ---

# 1. !!! BACKUP YOUR FILES FIRST !!!
# 2. Set the correct paths to your directories
target_directory = r"C:\path\to\your\LEFT_folder_with_correct_names" # Use 'r' before the string
source_directory = r"C:\path\to\your\RIGHT_folder_to_rename"

# 3. Run in Dry Run mode FIRST to check the proposed changes
rename_files(target_directory, source_directory, dry_run=True)

# 4. If the Dry Run output looks correct, change dry_run to False and run again
# logging.info("\n--- Running for REAL ---")
# rename_files(target_directory, source_directory, dry_run=False)