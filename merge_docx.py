from docx import Document
import os

def get_sections(doc):
    sections = []
    current_section = []
    
    # Get all paragraphs and tables
    elements = []
    for paragraph in doc.paragraphs:
        elements.append(('p', paragraph))
    for table in doc.tables:
        elements.append(('t', table))
    
    # Group elements into sections
    for element_type, element in elements:
        if element_type == 'p' and ('##################' in element.text or element.text.startswith('####')):
            if current_section:
                sections.append(current_section)
                current_section = []
        else:
            current_section.append((element_type, element))
    
    # Add the last section
    if current_section:
        sections.append(current_section)
    
    print(f"Found {len(sections)} sections")
    return sections

def merge_docx_files(questions_file, images_file, output_file):
    # Check if files exist
    if not os.path.exists(questions_file):
        print(f"Error: Questions file '{questions_file}' not found")
        return
    if not os.path.exists(images_file):
        print(f"Error: Images file '{images_file}' not found")
        return
    
    print(f"Loading documents...")
    
    try:
        # Load both documents
        questions_doc = Document(questions_file)
        images_doc = Document(images_file)
        print("Successfully loaded both documents")
    except Exception as e:
        print(f"Error loading documents: {str(e)}")
        return
    
    # Create new document for output
    output_doc = Document()
    
    print("\nProcessing questions document...")
    questions_sections = get_sections(questions_doc)
    
    print("\nProcessing images document...")
    images_sections = get_sections(images_doc)
    
    if not questions_sections:
        print("Warning: No sections found in questions document")
        return
    if not images_sections:
        print("Warning: No sections found in images document")
        return
    
    print(f"\nMerging {len(questions_sections)} sections from questions with {len(images_sections)} sections from images")
    
    # Merge sections
    for i in range(min(len(questions_sections), len(images_sections))):
        print(f"Processing section {i+1}...")
        
        # Add elements from questions section
        for element_type, element in questions_sections[i]:
            try:
                if element_type == 'p':
                    # Copy paragraph
                    p = output_doc.add_paragraph()
                    p.text = element.text
                    # Copy paragraph formatting
                    p.style = element.style
                    p.paragraph_format.alignment = element.paragraph_format.alignment
                elif element_type == 't':
                    # Copy table
                    new_table = output_doc.add_table(rows=len(element.rows), cols=len(element.columns))
                    for r_idx, row in enumerate(element.rows):
                        for c_idx, cell in enumerate(row.cells):
                            new_table.cell(r_idx, c_idx).text = cell.text
            except Exception as e:
                print(f"Warning: Could not add question element: {str(e)}")
        
        # Add elements from images section
        for element_type, element in images_sections[i]:
            try:
                if element_type == 'p':
                    # Copy paragraph with potential inline images
                    p = output_doc.add_paragraph()
                    p._element.append(element._element)
                elif element_type == 't':
                    # Copy table
                    new_table = output_doc.add_table(rows=len(element.rows), cols=len(element.columns))
                    for r_idx, row in enumerate(element.rows):
                        for c_idx, cell in enumerate(row.cells):
                            new_table.cell(r_idx, c_idx)._element.append(cell._element)
            except Exception as e:
                print(f"Warning: Could not add image element: {str(e)}")
        
        # Add separator except for the last section
        if i < min(len(questions_sections), len(images_sections)) - 1:
            output_doc.add_paragraph('##################')
    
    # Save the merged document
    try:
        # If output file exists, try to remove it first
        if os.path.exists(output_file):
            try:
                os.remove(output_file)
                print("Removed existing output file")
            except Exception as e:
                print(f"Warning: Could not remove existing file: {str(e)}")
                # Try with a different name
                base, ext = os.path.splitext(output_file)
                output_file = f"{base}_new{ext}"
                print(f"Will try to save as: {output_file}")
        
        output_doc.save(output_file)
        print(f"\nFiles merged successfully! Check {output_file}")
    except Exception as e:
        print(f"Error saving file: {str(e)}")
        print("Make sure the output file is not open in Word")

if __name__ == "__main__":
    questions_file = "questions.docx"
    images_file = "images.docx"
    output_file = "merged_output.docx"
    
    print("\nStarting document merge process...")
    print(f"Questions file: {questions_file}")
    print(f"Images file: {images_file}")
    print(f"Output file: {output_file}")
    
    merge_docx_files(questions_file, images_file, output_file) 