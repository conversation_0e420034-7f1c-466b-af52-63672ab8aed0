import re
import os
import tkinter as tk
from tkinter import filedialog
from docx import Document
import sys # Needed for sys.exit()

def add_q_to_questions_and_overwrite(file_path):
    """
    Reads a DOCX file, adds 'Q' before two- or three-digit question numbers,
    and saves the changes back to the SAME file (overwrites).

    Args:
        file_path (str): Path to the DOCX file to read and modify.

    Returns:
        int: The number of questions modified in the file, or -1 if an error occurred.
    """
    try:
        document = Document(file_path)
        original_paragraphs_text = [p.text for p in document.paragraphs]

        # --- MODIFIED REGEX ---
        # ^\s*      : Start of the line, optional leading whitespace
        # (\d{2,3}) : Capture exactly TWO or THREE digits (Group 1) <- Change is here
        # \.        : Followed by a literal dot
        # \s*       : Followed by optional whitespace
        # (.*)      : Capture the rest of the line (Group 2)
        question_pattern = re.compile(r"^\s*(\d{2,3})\.\s*(.*)")
        # --- <PERSON><PERSON> MODIFIED REGEX ---

        modified_count = 0
        paragraphs_to_modify = []

        for i, para_text in enumerate(original_paragraphs_text):
            text_to_check = para_text.rstrip()
            match = question_pattern.match(text_to_check)
            if match:
                number = match.group(1) # This will be '01', '99', '100', '123' etc.
                rest_of_line = match.group(2)
                new_text = f"Q{number}."
                if rest_of_line:
                    new_text += f" {rest_of_line}"

                leading_whitespace = para_text[:len(para_text) - len(para_text.lstrip())]
                trailing_whitespace = para_text[len(text_to_check):]
                reconstructed_text = leading_whitespace + new_text + trailing_whitespace

                if para_text != reconstructed_text:
                    paragraphs_to_modify.append((i, reconstructed_text))
                    modified_count += 1

        if modified_count > 0:
            for index, new_para_text in paragraphs_to_modify:
                 if index < len(document.paragraphs):
                    document.paragraphs[index].text = new_para_text
                 else:
                     print(f"  Warning: Paragraph index {index} out of bounds for file {os.path.basename(file_path)}")

            document.save(file_path)

        return modified_count

    except FileNotFoundError:
        print(f"Error: File not found '{os.path.basename(file_path)}'. Skipping.")
        return -1
    except PermissionError:
        print(f"Error: Permission denied to write file '{os.path.basename(file_path)}'. Is it open elsewhere? Skipping.")
        return -1
    except Exception as e:
        print(f"Error processing file '{os.path.basename(file_path)}': {e}")
        return -1

# --- Functions select_folder() and process_folder_overwrite() remain the same ---

def select_folder():
    """Opens a dialog for the user to select a folder."""
    root = tk.Tk()
    root.withdraw()
    folder_path = filedialog.askdirectory(title="Select Folder Containing DOCX Files")
    root.destroy()
    return folder_path

def process_folder_overwrite(folder_path):
    """
    Processes all DOCX files in the specified folder, overwriting them.
    Includes warning and confirmation.
    """
    if not folder_path:
        print("No folder selected. Exiting.")
        return

    if not os.path.isdir(folder_path):
        print(f"Error: Selected path is not a valid folder: '{folder_path}'")
        return

    print(f"Selected folder: {folder_path}")
    print("\n" + "="*60)
    print("!!! WARNING !!!")
    print("This script will modify the original .docx files directly.")
    print("There is NO undo function. It is strongly recommended")
    print("to BACK UP YOUR FOLDER before proceeding.")
    print("="*60 + "\n")

    confirm = input(f"Are you sure you want to modify files in '{folder_path}'? (y/N): ")

    if confirm.lower() != 'y':
        print("Operation cancelled by user.")
        return

    print("-" * 30)
    print("Starting file processing...")

    total_files_processed = 0
    total_questions_modified = 0
    files_with_errors = 0

    for filename in os.listdir(folder_path):
        if filename.lower().endswith(".docx") and not filename.startswith("~$"):
            file_path = os.path.join(folder_path, filename)

            print(f"Processing: {filename}")
            modified_count = add_q_to_questions_and_overwrite(file_path) # Using the updated function

            if modified_count > 0:
                print(f"  -> Modified {modified_count} questions. Changes saved to: {filename}")
                total_questions_modified += modified_count
                total_files_processed += 1
            elif modified_count == 0:
                print(f"  -> No matching 2- or 3-digit question lines found or no changes needed.")
                total_files_processed += 1
            else: # modified_count == -1
                print(f"  -> An error occurred during processing.")
                files_with_errors += 1

    print("-" * 30)
    if total_files_processed > 0 or files_with_errors > 0:
        print("Processing complete.")
        print(f"Total .docx files processed (or attempted): {total_files_processed + files_with_errors}")
        print(f"Total questions modified across files: {total_questions_modified}")
        if files_with_errors > 0:
            print(f"Files encountered errors during processing: {files_with_errors}")
        print(f"Modifications were applied directly to files in: {folder_path}")
    elif not confirm.lower() == 'y':
         pass # Message already printed
    else:
        print(f"No suitable '.docx' files found to process in the selected folder.")
    print("-" * 30)

# --- Main Execution ---
if __name__ == "__main__":
    selected_directory = select_folder()
    process_folder_overwrite(selected_directory)
    # input("Press Enter to exit...") # Uncomment if needed