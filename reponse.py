import os
import re
import tkinter as tk
from tkinter import filedialog, messagebox
import docx
from docx.opc.exceptions import PackageNotFoundError

def transform_responses(text):
    """Transform responses from 'Réponse : A, B, C' to 'Réponse : ABC'"""
    
    # Define regex pattern to match response lines
    # Looking for lines that start with "Réponse" or "Reponse" with optional accent and colon spacing
    pattern = r'^(R[ée]ponse\s*:)\s*(.*)$'
    
    lines = text.split('\n')
    transformed_lines = []
    changes = []
    all_responses = []
    
    for line in lines:
        match = re.match(pattern, line.strip())
        if match:
            prefix = "Réponse : "  # Standardize to this format
            response_text = match.group(2).strip()
            
            # Extract only capital letters A through E from the response
            letters = re.findall(r'[A-E]', response_text)
            
            # Special case handling
            if len(letters) == 0:
                # No letters found, replace with "/"
                new_response = "/"
                changes.append(f"{line.strip()} ---> {prefix}{new_response}")
                transformed_lines.append(f"{prefix}{new_response}")
                all_responses.append(f"{line.strip()} ---> {prefix}{new_response}")
            else:
                # For single letter or multiple letters, join them
                new_response = ''.join(letters)
                
                # Only add to changes if the response actually changed
                if new_response != response_text.replace(', ', ''):
                    changes.append(f"{line.strip()} ---> {prefix}{new_response}")
                    all_responses.append(f"{line.strip()} ---> {prefix}{new_response}")
                else:
                    # If no change, still record the response
                    all_responses.append(f"{line.strip()} (unchanged)")
                
                transformed_lines.append(f"{prefix}{new_response}")
        else:
            transformed_lines.append(line)
    
    return '\n'.join(transformed_lines), changes, all_responses

def process_docx_file(file_path):
    """Process a single DOCX file"""
    try:
        # Open the document
        doc = docx.Document(file_path)
        
        # Extract all text with paragraph breaks
        text = '\n'.join(paragraph.text for paragraph in doc.paragraphs)
        
        # Transform the responses
        transformed_text, changes, all_responses = transform_responses(text)
        
        # If no changes were made, return early
        if not changes and not all_responses:
            return [], []
        
        # Update document content
        # First, clear existing paragraphs
        for i in range(len(doc.paragraphs) - 1, -1, -1):
            p = doc.paragraphs[i]
            p._element.getparent().remove(p._element)
        
        # Add new paragraphs
        for line in transformed_text.split('\n'):
            doc.add_paragraph(line)
        
        # Save the document
        doc.save(file_path)
        return changes, all_responses
        
    except PackageNotFoundError:
        messagebox.showerror("Error", f"Could not open {file_path} as a DOCX file.")
        return [], []
    except Exception as e:
        messagebox.showerror("Error", f"Error processing {file_path}: {str(e)}")
        return [], []

def process_directory(directory_path):
    """Process all DOCX files in a directory and its subdirectories"""
    all_changes = []
    all_responses_summary = []
    
    for root, _, files in os.walk(directory_path):
        for file in files:
            if file.endswith('.docx'):
                file_path = os.path.join(root, file)
                changes, all_responses = process_docx_file(file_path)
                
                if changes or all_responses:
                    file_summary = [f"File: {file_path}"]
                    
                    if changes:
                        file_summary.extend([f"  Changed: {change}" for change in changes])
                    
                    file_summary.extend([f"  Response: {response}" for response in all_responses])
                    file_summary.append("")  # Empty line for separation
                    
                    all_changes.extend(file_summary)
                    all_responses_summary.extend(file_summary)
    
    return all_changes, all_responses_summary

def select_and_process():
    """Show file/folder selection dialog and process the selection"""
    root = tk.Tk()
    root.withdraw()  # Hide the root window
    
    # Ask the user if they want to process a file or directory
    choice = messagebox.askquestion("Selection", "Do you want to select a file?\n"
                                   "Select 'Yes' for file, 'No' for directory")
    
    all_changes = []
    all_responses_summary = []
    
    if choice == 'yes':
        # Select a single file
        file_path = filedialog.askopenfilename(
            title="Select a DOCX file",
            filetypes=[("Word Documents", "*.docx")]
        )
        
        if file_path:
            changes, all_responses = process_docx_file(file_path)
            if changes or all_responses:
                all_changes.append(f"File: {file_path}")
                if changes:
                    all_changes.extend([f"  Changed: {change}" for change in changes])
                all_changes.extend([f"  Response: {response}" for response in all_responses])
    else:
        # Select a directory
        directory_path = filedialog.askdirectory(title="Select a directory")
        
        if directory_path:
            all_changes, all_responses_summary = process_directory(directory_path)
    
    # Write summary to log file
    if all_changes:
        # Count the number of questions processed and changed
        question_count = len(all_responses_summary)
        changed_count = sum(1 for line in all_changes if "Changed:" in line)
        
        # Add the count to the summary
        all_changes.append(f"\nTotal questions processed: {question_count}")
        all_changes.append(f"Total questions changed: {changed_count}")
        
        # Save summary in the same directory as the script
        script_dir = os.path.dirname(os.path.abspath(__file__))
        log_path = os.path.join(script_dir, "response_transformation_summary.txt")
        
        with open(log_path, 'w', encoding='utf-8') as log_file:
            log_file.write('\n'.join(all_changes))
        
        messagebox.showinfo("Success", f"Processing complete!\n"
                           f"Total questions processed: {question_count}\n"
                           f"Total questions changed: {changed_count}\n"
                           f"Summary saved to {log_path}")
    else:
        messagebox.showinfo("Information", "No changes were made.")

if __name__ == "__main__":
    select_and_process()