import os
import tkinter as tk
from tkinter import filedialog, simpledialog, messagebox
from googleapiclient.discovery import build
from googleapiclient.http import MediaFileUpload
from google.oauth2 import service_account

# 1️⃣ Load Google Drive API credentials
SCOPES = ['https://www.googleapis.com/auth/drive']
SERVICE_ACCOUNT_FILE = 'credentials.json'  # Ensure this file is in the same directory

creds = service_account.Credentials.from_service_account_file(
    SERVICE_ACCOUNT_FILE, scopes=SCOPES)

drive_service = build('drive', 'v3', credentials=creds)

# 2️⃣ Function to upload and rename a single file
def upload_and_rename(file_path, drive_folder_id=None):
    """
    Uploads a file to Google Drive and renames it by replacing "_" with ":".
    """
    filename = os.path.basename(file_path)
    new_filename = filename.replace("_", ":")  # Replace _ with :

    file_metadata = {'name': filename}
    if drive_folder_id:
        file_metadata['parents'] = [drive_folder_id]  # Upload inside the specified folder

    media = MediaFileUpload(file_path, mimetype='application/vnd.openxmlformats-officedocument.wordprocessingml.document')
    uploaded_file = drive_service.files().create(body=file_metadata, media_body=media, fields='id,name').execute()

    file_id = uploaded_file['id']

    # Rename the file on Google Drive
    drive_service.files().update(fileId=file_id, body={'name': new_filename}).execute()

    print(f"✅ Uploaded & Renamed: {filename} -> {new_filename}")

# 3️⃣ Function to select a folder and upload all .docx files
def select_folder_and_upload():
    """
    Opens a GUI to select a folder, then uploads all .docx files from that folder.
    """
    root = tk.Tk()
    root.withdraw()  # Hide the main Tkinter window

    folder_path = filedialog.askdirectory(title="Select Folder Containing .docx Files")
    
    if not folder_path:
        messagebox.showerror("Error", "No folder selected! Please select a valid folder.")
        return

    # Ask user to input the Google Drive Folder ID
    drive_folder_id = simple_input("Enter Google Drive Folder ID (or leave empty for root):")
    if drive_folder_id == "":
        drive_folder_id = None  # Upload to root if no folder ID is provided

    files_uploaded = 0
    for file in os.listdir(folder_path):
        if file.endswith(".docx"):
            file_path = os.path.join(folder_path, file)
            upload_and_rename(file_path, drive_folder_id)
            files_uploaded += 1

    messagebox.showinfo("Upload Complete", f"🎉 Done! Uploaded & Renamed {files_uploaded} files.")

# 4️⃣ Simple input function using Tkinter's simpledialog
def simple_input(prompt):
    """
    Opens a simple input dialog for user input.
    """
    root = tk.Tk()
    root.withdraw()  # Hide the main window
    return simpledialog.askstring("Input Required", prompt)

# 5️⃣ Run the GUI folder selection
if __name__ == "__main__":
    select_folder_and_upload()
